# 本地知识图谱服务安装完成

## 🎉 安装成功！

已成功安装并配置了无需付费API的本地知识图谱服务 `mcp-knowledge-graph`。

## 📁 安装位置

- **服务器**: 全局安装在 npm 中
- **数据存储**: `/home/<USER>/.local/share/knowledge-graph/memory.jsonl`
- **MCP配置**: `~/.config/Code/User/mcp.json`

## 🔧 配置详情

已添加到MCP配置文件的服务器配置：

```json
"knowledge-graph": {
  "type": "stdio",
  "command": "npx",
  "args": [
    "-y",
    "mcp-knowledge-graph",
    "--memory-path",
    "/home/<USER>/.local/share/knowledge-graph/memory.jsonl"
  ],
  "gallery": true
}
```

## 🚀 功能特性

### 核心概念
1. **实体 (Entities)**: 知识图谱中的主要节点
   - 唯一名称标识符
   - 实体类型（如"person", "organization", "event"）
   - 观察列表

2. **关系 (Relations)**: 实体间的有向连接
   - 始终以主动语态存储
   - 描述实体间的交互或关联

3. **观察 (Observations)**: 关于实体的离散信息片段
   - 以字符串形式存储
   - 附加到特定实体
   - 可独立添加或删除

### 可用工具
- `create_entities`: 创建多个新实体
- `create_relations`: 创建实体间关系
- `add_observations`: 为实体添加观察
- `delete_entities`: 删除实体及其关系
- `delete_observations`: 删除特定观察
- `delete_relations`: 删除特定关系
- `read_graph`: 读取整个知识图谱
- `search_nodes`: 基于查询搜索节点
- `open_nodes`: 按名称检索特定节点

## 💡 使用方法

1. **重启 VS Code** 以加载新的MCP配置
2. 在支持MCP的AI助手中，知识图谱服务将自动可用
3. 可以通过对话自然地构建和查询知识图谱

## 📊 数据持久化

- 所有数据存储在本地文件系统中
- 无需外部数据库或云服务
- 完全离线工作，保护隐私

## 🔒 隐私保护

- ✅ 完全本地运行
- ✅ 无需API密钥
- ✅ 数据不会发送到外部服务
- ✅ 完全免费使用

## 🛠️ 故障排除

如果遇到问题：

1. 确保 Node.js 已安装
2. 检查数据目录权限：`ls -la ~/.local/share/knowledge-graph/`
3. 验证MCP配置：`python3 -m json.tool ~/.config/Code/User/mcp.json`
4. 重启 VS Code

## 📝 示例用法

在AI对话中，你可以：
- "记住我喜欢喝咖啡"
- "John Smith 在 ExampleCorp 工作"
- "搜索所有与编程相关的信息"
- "显示我的所有项目"

知识图谱会自动构建实体、关系和观察，形成持久的记忆系统。
