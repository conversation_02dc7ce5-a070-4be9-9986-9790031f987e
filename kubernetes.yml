---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: macos-pvc
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 64Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: macos
  labels:
    name: macos
spec:
  replicas: 1
  selector:
    matchLabels:
      app: macos
  template:
    metadata:
      labels:
        app: macos
    spec:
      containers:
      - name: macos
        image: dockurr/macos
        env:
        - name: VERSION
          value: "13"
        - name: DISK_SIZE
          value: "64G"
        ports:
          - containerPort: 8006
            name: http
            protocol: TCP
          - containerPort: 5900
            name: vnc
            protocol: TCP
        securityContext:
          capabilities:
            add:
            - NET_ADMIN
          privileged: true
        volumeMounts:
        - mountPath: /storage
          name: storage
        - mountPath: /dev/kvm
          name: dev-kvm
        - mountPath: /dev/net/tun
          name: dev-tun
      terminationGracePeriodSeconds: 120
      volumes:
      - name: storage
        persistentVolumeClaim:
          claimName: macos-pvc
      - hostPath:
          path: /dev/kvm
        name: dev-kvm
      - hostPath:
          path: /dev/net/tun
          type: CharDevice
        name: dev-tun
---
apiVersion: v1
kind: Service
metadata:
  name: macos
spec:
  internalTrafficPolicy: Cluster
  ports:
    - name: http
      port: 8006
      protocol: TCP
      targetPort: 8006
    - name: vnc
      port: 5900
      protocol: TCP
      targetPort: 5900
  selector:
    app: macos
  type: ClusterIP
