name: "\U0001F41E Bug report"
description: Create a report to help us improve the container
title: "[Bug]: "
labels: ["bug"]
body:
  - type: input
    id: os
    attributes:
      label: Operating system
      description: Your Linux distribution (can be shown by `lsb_release -a`).
      placeholder: e.g. Ubuntu 24.04
    validations:
      required: true
  - type: textarea
    id: summary
    attributes:
      label: Description
      description: Describe the expected behaviour, the actual behaviour, and the steps to reproduce.
    validations:
      required: true
  - type: textarea
    id: compose
    attributes:
      label: Docker compose
      description: The compose file (or otherwise the `docker run` command used).
      render: yaml
    validations:
      required: true
  - type: textarea
    id: log
    attributes:
      label: Docker log
      description: The logfile of the container (as shown by `docker logs macos`).
      render: shell
    validations:
      required: true
  - type: textarea
    id: screenshot
    attributes:
      label: Screenshots (optional)
      description: Screenshots that might help to make the problem more clear.
    validations:
      required: false
